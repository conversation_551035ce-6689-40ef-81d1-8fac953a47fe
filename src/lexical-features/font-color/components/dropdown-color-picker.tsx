"use client";

import React, { useEffect, useState } from "react";
import { ColorPickerWrapper } from "./ColorPicker";
import { FontColorIcon } from "../icons/FontColorIcon";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@@/shared/ui/dropdown-menu";

export const DropdownColorPicker = () => {
	const [fontColor, setFontColor] = useState<string | undefined>("");
	const [CSSVariable, setCSSVariable] = useState<string | null>(null);

	// We'll work on those later...
	const handleOpenChange = () => {};
	const handleFontColorChange = () => {};
	const handleApplyStyles = () => {};

	return (
		<DropdownMenu onOpenChange={handleOpenChange}>
			<DropdownMenuTrigger className="toolbar-popup__button toolbar-popup__button-bold">
				<FontColorIcon underscoreColor={fontColor} />
			</DropdownMenuTrigger>
			<DropdownMenuContent side="top">
				<ColorPicker
					onApplyStyles={handleApplyStyles}
					fontColor={fontColor}
					onFontColorChange={handleFontColorChange}
				/>
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
