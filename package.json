{"name": "hard-chor", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types"}, "dependencies": {"@biomejs/biome": "1.9.4", "@gsap/react": "^2.1.2", "@payloadcms/db-mongodb": "^3.48.0", "@payloadcms/live-preview-react": "^3.48.0", "@payloadcms/next": "^3.48.0", "@payloadcms/payload-cloud": "^3.48.0", "@payloadcms/plugin-redirects": "^3.48.0", "@payloadcms/plugin-seo": "^3.48.0", "@payloadcms/richtext-lexical": "^3.48.0", "@payloadcms/storage-s3": "^3.48.0", "@payloadcms/ui": "^3.48.0", "@radix-ui/react-accordion": "^1.2.11", "clsx": "^2.1.1", "colortranslator": "^5.0.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "graphql": "^16.8.1", "gsap": "^3.13.0", "jsonwebtoken": "^9.0.2", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "next": "15.4.2", "next-view-transitions": "^0.3.4", "payload": "^3.48.0", "react": "19.1.0", "react-colorful": "^5.6.1", "react-dom": "19.1.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tempus": "1.0.0-dev.12", "tw-animate-css": "^1.3.5", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}